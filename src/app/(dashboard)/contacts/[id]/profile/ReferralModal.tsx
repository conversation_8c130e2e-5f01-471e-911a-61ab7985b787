import SearchContact from '@/components/elements/search/SearchContact';
import StringInput from '@/components/Input/StringInput';
import { ToastMessages } from '@/constants/toast-messages';
import { useGetSingleClientHook } from '@/hooks/receptionist/contacts/useGetSingleClientHook';
import { FullClient } from '@/shared/interface/clients';
import { Box, Center, Stack, Text, useDisclosure } from '@chakra-ui/react';
import React, { FormEvent, useState } from 'react';
import { toaster } from '@/components/ui/toaster';
import { CustomModal } from '@/components/elements/modal/custom-modal';
import { Button } from '@/components/ui/button';
import { useQueryClient } from '@tanstack/react-query';
import { queryKey } from '@/constants/query-key';
import { useGetAllReferralsQuery } from '@/api/referrals/get-all-referrals';
import { useAddReferralMutation } from '@/api/referrals/useAddReferral';
import { FaPlus } from 'react-icons/fa';

type GetSingleClientHookReturnType = ReturnType<typeof useGetSingleClientHook>;
export default function AddReferralModal({
  data,
  getClientHook,
  section,
  variant = 1,
}: {
  getClientHook: GetSingleClientHookReturnType;
  data: FullClient;
  section?: any;
  variant?: number;
}) {
  const { onOpen, onClose, open } = useDisclosure();

  const [searchResult, setSearchResult] = useState<Array<any>>([]);
  const [selectedClient, setSelectedClient] = useState<any>();
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();
  const { data: existingReferral } = useGetAllReferralsQuery(
    selectedClient?.id,
    {
      enabled: Boolean(selectedClient?.id),
    }
  ) as any;
  const { mutateAsync } = useAddReferralMutation();

  // chehck and see if the selected client has already been referred(is a referee)
  const isSelectedClientAReferee = existingReferral?.some(
    (item: any) => item?.referee?.id === selectedClient?.id
  );

  const addReferral = async (e: FormEvent<HTMLFormElement>) => {
    try {
      e.preventDefault();
      setLoading(true);
      if (isSelectedClientAReferee) {
        return toaster.create({
          description: 'This client has already been referred',
          type: 'error',
        });
      }
      if (!selectedClient?.id || !data.id) {
        return toaster.create({
          description: 'Cannot insert null value',
          type: 'error',
        });
      }
      if (selectedClient?.id === data.id) {
        return toaster.create({
          description: `Oops!! Clients can't refer themselves`,
          type: 'error',
        });
      }
      await mutateAsync({
        referee_id: selectedClient?.id,
        referrer_id: data.id,
        credit: 1,
      });
      queryClient.invalidateQueries({
        queryKey: [queryKey.referrals.getAllReferrals, data?.id],
      });
      await getClientHook.refetch();
      setSelectedClient(null);
      setSearchResult([]);
      onClose();
    } catch (error: any) {
      toaster.create({
        description: error?.message || ToastMessages.somethingWrong,
        type: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box width={'100%'} display={'flex'} justifyContent={'flex-end'}>
      {section === 'profile' ? (
        <Center
          cursor="pointer"
          onClick={onOpen}
          h="20px"
          w="20px"
          bg={variant === 1 ? 'primary.500' : undefined}
          rounded={variant === 1 ? undefined : 'full'}
          _hover={{ bg: 'gray.50' }}
        >
          <FaPlus
            size={variant === 1 ? 10 : 14}
            color={variant === 1 ? 'white' : 'black'}
          />
        </Center>
      ) : (
        <Text
          onClick={onOpen}
          // cursor={'pointer'}
          // color={'primary.500'}
          // fontWeight={600}
        >
          Add Referral
        </Text>
      )}

      <CustomModal w={'40rem'} onOpenChange={onClose} open={open}>
        <form onSubmit={addReferral}>
          <Stack gap={'2rem'} my={'2rem'}>
            <StringInput
              inputProps={{
                name: 'client',
                readOnly: true,
                disabled: true,
                defaultValue: `${data?.first_name} ${data?.last_name}`,
              }}
              fieldProps={{
                label: 'Referrer',
              }}
            />
            <Stack>
              <label className="font-medium text-gray-900">Lookup Client</label>
              <SearchContact
                setSearchResult={(e: any) => {
                  setSearchResult(e);
                }}
                searchResult={searchResult}
                selectExistingUser={setSelectedClient}
              />
            </Stack>

            {selectedClient && (
              <StringInput
                inputProps={{
                  name: 'client',
                  readOnly: true,
                  value: `${selectedClient?.first_name} ${selectedClient?.last_name}`,
                }}
                fieldProps={{
                  label: 'Referee',
                }}
              />
            )}

            <Button bg={'primary.500'} loading={loading} type="submit">
              Add
            </Button>
          </Stack>
        </form>
      </CustomModal>
    </Box>
  );
}
